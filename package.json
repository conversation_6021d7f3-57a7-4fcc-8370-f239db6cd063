{"name": "mev-arbitrage-bot", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "next dev -p 3000", "dev:backend": "node enhanced-backend.mjs", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "next build", "build:backend": "tsc -p backend/tsconfig.json", "start": "node scripts/production-startup-command.mjs", "start:backend": "node enhanced-backend.mjs", "start:orchestrator": "node scripts/master-startup-orchestrator.mjs", "start:databases": "node scripts/database-initialization-manager.mjs", "setup:db": "node scripts/setup-ml-database.js", "setup:schema": "node scripts/validate-database-schema.mjs", "verify": "tsx scripts/verify-system.ts", "verify:system": "tsx scripts/verify-system.ts", "validate:system": "tsx scripts/validate-system-integration.ts", "validate:comprehensive": "node scripts/comprehensive-system-validator.mjs", "validate:schema": "node scripts/validate-database-schema.mjs", "monitor:health": "node scripts/system-health-monitor.mjs", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:coverage:analyze": "tsx scripts/test-coverage-analyzer.ts", "test:contracts": "hardhat test", "test:integration": "jest --testPathPattern=tests/integration", "test:performance": "jest --testPathPattern=tests/performance", "test:e2e": "jest --testPathPattern=tests/e2e", "test:all": "npm run test:contracts && npm run test && npm run test:integration && npm run test:performance", "test:comprehensive": "tsx scripts/run-integration-tests.ts", "test:hardhat": "hardhat test", "test:hardhat:coverage": "hardhat coverage", "test:local": "hardhat test test/hardhat-local-testing.test.ts", "test:mainnet-fork": "hardhat test --network hardhat", "test:testnets": "jest tests/integration/multi-chain-testnet-integration.test.ts", "test:testnet:goerli": "hardhat test --network goerli", "test:testnet:mumbai": "hardhat test --network mumbai", "test:testnet:bsc": "hardhat test --network bscTestnet", "test:price-feeds": "hardhat test test/price-discovery.test.ts", "test:token-filtering": "hardhat test test/token-filtering.test.ts", "test:opportunity-detection": "hardhat test test/opportunity-detection.test.ts", "test:execution-queue": "hardhat test test/execution-queue.test.ts", "test:mev-protection": "hardhat test test/mev-protection.test.ts", "test:flash-loans": "hardhat test test/flash-loan-integration.test.ts", "test:risk-management": "hardhat test test/risk-management.test.ts", "test:deployment": "hardhat test test/deployment-validation.test.ts", "benchmark": "tsx scripts/system-performance-benchmark.ts", "benchmark:websocket": "tsx scripts/websocket-load-test.ts", "benchmark:gas": "hardhat test test/gas-optimization.test.ts", "benchmark:throughput": "hardhat test test/throughput-benchmark.test.ts", "demo:integration": "tsx scripts/demo-system-integration.ts", "compile:contracts": "hardhat compile", "deploy:contracts": "hardhat run scripts/deploy.ts", "deploy:testnets": "tsx scripts/deploy-testnets.ts", "deploy:goerli": "hardhat run scripts/deploy.ts --network goerli", "deploy:mumbai": "hardhat run scripts/deploy.ts --network mumbai", "deploy:bsc": "hardhat run scripts/deploy.ts --network bscTestnet", "validate:testnets": "tsx scripts/validate-testnet-environment.ts", "test:multi-chain": "npm run validate:testnets && npm run test:testnets", "test:final-integration": "tsx scripts/final-integration-testing.ts", "test:production-ready": "npm run test:final-integration", "test:e2e-comprehensive": "tsx scripts/comprehensive-e2e-test.ts", "test:system-complete": "npm run test:e2e-comprehensive"}, "dependencies": {"@google/genai": "^1.1.0", "@influxdata/influxdb-client": "^1.35.0", "@sentry/nextjs": "^7.99.0", "@supabase/supabase-js": "^2.49.8", "@tailwindcss/forms": "^0.5.10", "@tanstack/react-query": "^5.17.0", "@types/pg": "^8.15.4", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "chart.js": "^4.4.1", "chartjs-adapter-date-fns": "^3.0.0", "clsx": "^2.1.0", "cors": "^2.8.5", "date-fns": "^3.2.0", "decimal.js": "^10.4.3", "dotenv": "^16.5.0", "ethers": "^6.8.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "framer-motion": "^10.18.0", "lodash": "^4.17.21", "lucide-react": "^0.312.0", "next": "^14.1.0", "next-themes": "^0.2.1", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "pg": "^8.16.3", "postcss": "^8.5.3", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-use-websocket": "^4.5.0", "react-window": "^1.8.8", "redis": "^4.6.10", "tailwind-merge": "^2.2.1", "tailwindcss": "^3.4.1", "uuid": "^9.0.1", "web3": "^4.2.2", "winston": "^3.11.0", "ws": "^8.14.2", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@nomicfoundation/hardhat-foundry": "^1.1.4", "@nomicfoundation/hardhat-network-helpers": "^1.0.13", "@nomicfoundation/hardhat-toolbox": "^4.0.0", "@openzeppelin/contracts": "^5.0.0", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/lodash": "^4.14.202", "@types/mocha": "^10.0.6", "@types/node": "^22.14.0", "@types/node-cron": "^3.0.11", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@types/react-window": "^1.8.8", "@types/supertest": "^6.0.2", "@types/uuid": "^9.0.7", "@types/ws": "^8.5.10", "chai": "^4.3.10", "concurrently": "^8.2.2", "eslint": "^8.56.0", "eslint-config-next": "^14.1.0", "hardhat": "^2.19.1", "hardhat-gas-reporter": "^1.0.10", "jest": "^29.7.0", "mocha": "^10.2.0", "nodemon": "^3.0.2", "solidity-coverage": "^0.8.5", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "tsx": "^4.6.2", "typescript": "~5.7.2"}}